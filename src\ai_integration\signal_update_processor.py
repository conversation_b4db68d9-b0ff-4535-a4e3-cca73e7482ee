"""
Signal Update Processor for MignalyBot
Processes active signals and generates immediate performance updates with PnL images
"""

import asyncio
import logging
import random
from datetime import datetime, timedelta
from typing import List, Optional
from sqlalchemy import select, and_

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import TradingSignal as Signal, SignalStatus, Channel, Post, PostType, PostStatus, TakeProfitHit
from src.ai_integration.qwen_client import QwenClient
from src.ai_integration.content_generator import generate_pnl_image_from_signal, is_forex_pair, calculate_pips, get_performance_caption
from src.strategies.processor import get_fresh_candle_data_for_signal, process_multi_tp_signal
from src.utils.helpers import get_current_time, get_utc_time, to_utc
from src.telegram.bot import send_message_to_channel

logger = logging.getLogger(__name__)

async def process_signal_updates():
    """
    Process active signals and generate immediate performance updates
    """
    start_time = get_utc_time()
    logger.info(f"🔄 Starting signal update processing at {start_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")

    try:
        async for db in get_async_db():
            # Get all active signals that need updates
            signals_start = get_utc_time()
            active_signals = await get_active_signals_for_updates(db)
            signals_duration = (get_utc_time() - signals_start).total_seconds()

            if not active_signals:
                logger.info("📊 No active signals found for updates")
                return

            logger.info(f"📊 Found {len(active_signals)} active signals to process (query took {signals_duration:.2f}s)")

            # Get all active channels
            channels_start = get_utc_time()
            channels = await get_active_channels(db)
            channels_duration = (get_utc_time() - channels_start).total_seconds()

            if not channels:
                logger.info("📺 No active channels found")
                return

            logger.info(f"📺 Found {len(channels)} active channels (query took {channels_duration:.2f}s)")

            # Initialize Qwen client
            qwen_client = QwenClient()

            # Process each signal
            processed_count = 0
            for signal in active_signals:
                try:
                    signal_start = get_utc_time()
                    await process_single_signal_update(db, signal, channels, qwen_client)
                    signal_duration = (get_utc_time() - signal_start).total_seconds()
                    processed_count += 1
                    logger.info(f"⏱️ Signal {signal.id} processing completed in {signal_duration:.2f}s")
                except Exception as e:
                    logger.error(f"Error processing signal {signal.id}: {e}", exc_info=True)
                    continue

            total_duration = (get_utc_time() - start_time).total_seconds()
            logger.info(f"✅ Signal update processing completed: {processed_count}/{len(active_signals)} signals processed in {total_duration:.2f}s")
            
            logger.info("✅ Signal update processing completed")
            
    except Exception as e:
        logger.error(f"Error in signal update processing: {e}", exc_info=True)

async def get_active_signals_for_updates(db) -> List[Signal]:
    """
    Get active signals that need status updates
    """
    try:
        # Get signals that are active or have hit partial TPs
        if is_sqlite_db():
            result = db.execute(
                select(Signal).where(
                    Signal.status.in_([
                        SignalStatus.ACTIVE,
                        SignalStatus.TP1_HIT,
                        SignalStatus.TP2_HIT
                    ])
                )
            )
        else:
            result = await db.execute(
                select(Signal).where(
                    Signal.status.in_([
                        SignalStatus.ACTIVE,
                        SignalStatus.TP1_HIT,
                        SignalStatus.TP2_HIT
                    ])
                )
            )
        
        signals = result.scalars().all()
        return signals
        
    except Exception as e:
        logger.error(f"Error getting active signals: {e}", exc_info=True)
        return []

async def get_active_channels(db) -> List[Channel]:
    """
    Get all active channels
    """
    try:
        if is_sqlite_db():
            result = db.execute(select(Channel).where(Channel.active == True))
        else:
            result = await db.execute(select(Channel).where(Channel.active == True))

        channels = result.scalars().all()
        return channels

    except Exception as e:
        logger.error(f"Error getting active channels: {e}", exc_info=True)
        return []

async def process_single_signal_update(db, signal: Signal, channels: List[Channel], qwen_client: QwenClient):
    """
    Process a single signal for updates
    """
    signal_start = get_utc_time()
    signal_id = signal.id  # Store ID early in case signal becomes stale
    logger.info(f"🔍 Processing signal {signal_id} ({signal.symbol} {signal.direction.upper()}) at {signal_start.strftime('%H:%M:%S UTC')}")

    try:
        # Refresh signal from database to ensure we have the latest state
        if is_sqlite_db():
            fresh_signal_result = db.execute(select(Signal).where(Signal.id == signal_id))
        else:
            fresh_signal_result = await db.execute(select(Signal).where(Signal.id == signal_id))

        fresh_signal = fresh_signal_result.scalars().first()
        if not fresh_signal:
            logger.warning(f"Signal {signal_id} no longer exists in database, skipping")
            return

        # Use fresh signal instance for all operations
        signal = fresh_signal
        logger.info(f"🔄 Refreshed signal {signal_id} from database - current status: {signal.status.value}")

        # Get fresh candle data
        candle_start = get_utc_time()
        latest_candle = await get_fresh_candle_data_for_signal(signal)
        candle_duration = (get_utc_time() - candle_start).total_seconds()

        if not latest_candle:
            logger.warning(f"No fresh candle data available for signal {signal_id} (query took {candle_duration:.2f}s)")
            return

        logger.info(f"📊 Fresh candle data retrieved for signal {signal_id} in {candle_duration:.2f}s - Price: {latest_candle.close}")

        # Store old status for comparison
        old_status = signal.status

        # Update signal status using existing multi-TP logic
        status_start = get_utc_time()
        signal_updated = False
        if signal.take_profit_1:
            signal_updated = await process_multi_tp_signal(signal, latest_candle, db)
        status_duration = (get_utc_time() - status_start).total_seconds()

        logger.info(f"⚡ Signal status processing completed in {status_duration:.2f}s - Updated: {signal_updated}")

        # Check if signal status changed
        if signal_updated and signal.status != old_status:
            logger.info(f"📊 Signal {signal_id} status changed from {old_status.value} to {signal.status.value}")

            # Generate immediate performance updates for all channels
            updates_start = get_utc_time()
            updates_generated = 0
            for channel in channels:
                try:
                    # Check if performance posts are enabled for this channel
                    post_types = set(channel.post_types.split(",")) if channel.post_types else set()

                    if "performance" in post_types:
                        update_start = get_utc_time()
                        await generate_immediate_performance_update(db, signal, channel, qwen_client, old_status)
                        update_duration = (get_utc_time() - update_start).total_seconds()
                        updates_generated += 1
                        logger.info(f"📈 Performance update for channel {channel.name} generated in {update_duration:.2f}s")
                    else:
                        logger.info(f"⏭️ Performance posts disabled for channel {channel.name}")

                except Exception as e:
                logger.error(f"Error generating performance update for channel {channel.name}: {e}", exc_info=True)
                continue

            updates_duration = (get_utc_time() - updates_start).total_seconds()
            logger.info(f"🎯 Generated {updates_generated} performance updates in {updates_duration:.2f}s")

        # Commit signal status changes
        try:
            if is_sqlite_db():
                db.commit()
            else:
                await db.commit()
        except Exception as e:
            logger.error(f"Error committing signal status changes: {e}", exc_info=True)

    except Exception as e:
        # Handle ObjectDeletedError and other database exceptions
        from sqlalchemy.orm.exc import ObjectDeletedError
        if isinstance(e, ObjectDeletedError):
            logger.warning(f"Signal {signal_id} was deleted during processing, skipping")
        else:
            logger.error(f"Error processing signal {signal_id}: {e}", exc_info=True)

        # Try to rollback any pending changes
        try:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
        except Exception as rollback_error:
            logger.error(f"Error during rollback for signal {signal_id}: {rollback_error}", exc_info=True)

async def generate_immediate_performance_update(db, signal: Signal, channel: Channel, qwen_client: QwenClient, old_status: SignalStatus):
    """
    Generate immediate performance update post that replies to original signal
    """
    update_start = get_utc_time()
    logger.info(f"🎯 Generating immediate performance update for signal {signal.id} in channel {channel.name} at {update_start.strftime('%H:%M:%S UTC')}")

    try:
        # Check if we already sent an update for this status change
        duplicate_check_start = get_utc_time()
        now = get_current_time()
        now_utc = get_utc_time()
        recent_cutoff = now_utc - timedelta(minutes=30)  # Don't duplicate updates within 30 minutes
        
        if is_sqlite_db():
            existing_update = db.execute(
                select(Post).where(
                    and_(
                        Post.signal_id == signal.id,
                        Post.channel_id == channel.id,
                        Post.type == PostType.PERFORMANCE,
                        Post.created_at >= recent_cutoff
                    )
                )
            ).scalars().first()
        else:
            existing_update = (await db.execute(
                select(Post).where(
                    and_(
                        Post.signal_id == signal.id,
                        Post.channel_id == channel.id,
                        Post.type == PostType.PERFORMANCE,
                        Post.created_at >= recent_cutoff
                    )
                )
            )).scalars().first()
        
        if existing_update:
            logger.info(f"Recent performance update already exists for signal {signal.id} in channel {channel.name}")
            return
        
        # Find original signal post to reply to
        reply_to_message_id = await find_original_signal_post(db, signal, channel)
        
        # Determine TP level for the update
        tp_level = None
        if signal.status == SignalStatus.TP1_HIT:
            tp_level = 1
        elif signal.status == SignalStatus.TP2_HIT:
            tp_level = 2
        elif signal.status == SignalStatus.TP3_HIT or signal.status == SignalStatus.ALL_TP_HIT:
            tp_level = 3

        # Get TP hit record for accurate price data
        tp_hit_record = None
        if tp_level:
            try:
                if is_sqlite_db():
                    tp_hit_result = db.execute(
                        select(TakeProfitHit).where(
                            TakeProfitHit.signal_id == signal.id,
                            TakeProfitHit.tp_level == tp_level
                        ).order_by(TakeProfitHit.hit_time.desc()).limit(1)
                    )
                else:
                    tp_hit_result = await db.execute(
                        select(TakeProfitHit).where(
                            TakeProfitHit.signal_id == signal.id,
                            TakeProfitHit.tp_level == tp_level
                        ).order_by(TakeProfitHit.hit_time.desc()).limit(1)
                    )
                tp_hit_record = tp_hit_result.scalars().first()
                if tp_hit_record:
                    logger.info(f"📊 Found TP{tp_level} hit record with price {tp_hit_record.tp_price}")
            except Exception as e:
                logger.warning(f"Could not fetch TP hit record: {e}")

        # Generate PnL image
        logger.info(f"🖼️ Generating PnL image for signal {signal.id} status update...")
        image_path = generate_pnl_image_from_signal(signal, tp_level=tp_level, tp_hit_record=tp_hit_record)
        
        # Generate caption with break-even messaging for TP1
        safe_attrs = ensure_channel_attributes(channel)

        # Use appropriate caption based on TP level
        if tp_level == 1:
            content = get_performance_caption('tp1_hit_break_even', safe_attrs['language'])
        elif tp_level:
            content = get_performance_caption('tp_hit', safe_attrs['language'], level=tp_level)
        else:
            content = get_performance_caption('trade_result', safe_attrs['language'])

        # Add pips information for forex symbols
        if is_forex_pair(signal.symbol):
            if tp_level and hasattr(signal, f'take_profit_{tp_level}'):
                tp_price = getattr(signal, f'take_profit_{tp_level}')
                if tp_price and signal.entry_price:
                    pips = calculate_pips(signal.entry_price, tp_price, signal.symbol)
                    if pips > 0:
                        pips_caption = get_performance_caption('pips_profit', safe_attrs['language'], pips=pips)
                        content += f"\n{pips_caption}"
            elif signal.exit_price and signal.entry_price:
                pips = calculate_pips(signal.entry_price, signal.exit_price, signal.symbol)
                if pips > 0:
                    # Determine if profit or loss
                    is_profit = ((signal.direction == "buy" and signal.exit_price > signal.entry_price) or
                               (signal.direction == "sell" and signal.exit_price < signal.entry_price))
                    pips_type = 'pips_profit' if is_profit else 'pips_loss'
                    pips_caption = get_performance_caption(pips_type, safe_attrs['language'], pips=pips)
                    content += f"\n{pips_caption}"

        if not image_path and not content:
            logger.warning(f"Both image and content generation failed for signal {signal.id}")
            return
        
        # Create immediate post (send within 1-3 minutes)
        # Use UTC time for scheduling since post scheduler compares with UTC
        now_utc = get_utc_time()
        scheduled_time_utc = now_utc + timedelta(minutes=random.randint(1, 3))

        logger.info(f"📅 Scheduling performance update for {scheduled_time_utc.strftime('%Y-%m-%d %H:%M:%S UTC')} (in {(scheduled_time_utc - now_utc).total_seconds()/60:.1f} minutes)")

        post = Post(
            channel_id=channel.id,
            type=PostType.PERFORMANCE,
            content=content or "Signal update",
            status=PostStatus.SCHEDULED,
            scheduled_time=scheduled_time_utc,  # Store in UTC
            signal_id=signal.id,
            image_path=image_path,
            reply_to_message_id=reply_to_message_id,
            created_at=now_utc  # Also store created_at in UTC
        )
        
        db.add(post)
        
        if is_sqlite_db():
            db.commit()
        else:
            await db.commit()
        
        logger.info(f"✅ Created immediate performance update for signal {signal.id} in channel {channel.name}")
        
    except Exception as e:
        logger.error(f"Error generating immediate performance update: {e}", exc_info=True)

async def find_original_signal_post(db, signal: Signal, channel: Channel) -> Optional[int]:
    """
    Find the original signal post to reply to
    """
    try:
        if is_sqlite_db():
            original_post = db.execute(
                select(Post).where(
                    and_(
                        Post.signal_id == signal.id,
                        Post.channel_id == channel.id,
                        Post.type == PostType.SIGNAL,
                        Post.status == PostStatus.PUBLISHED
                    )
                ).order_by(Post.created_at.asc())
            ).scalars().first()
        else:
            original_post = (await db.execute(
                select(Post).where(
                    and_(
                        Post.signal_id == signal.id,
                        Post.channel_id == channel.id,
                        Post.type == PostType.SIGNAL,
                        Post.status == PostStatus.PUBLISHED
                    )
                ).order_by(Post.created_at.asc())
            )).scalars().first()
        
        if original_post and original_post.message_id:
            # Get the first message ID if multiple
            message_ids = original_post.message_id.split(",")
            return int(message_ids[0]) if message_ids[0].isdigit() else None
        
        return None
        
    except Exception as e:
        logger.error(f"Error finding original signal post: {e}", exc_info=True)
        return None

def ensure_channel_attributes(channel: Channel) -> dict:
    """
    Ensure channel has required attributes with defaults
    """
    return {
        'language': getattr(channel, 'language', 'en'),
        'brand_name': getattr(channel, 'brand_name', 'MignalyBot')
    }
